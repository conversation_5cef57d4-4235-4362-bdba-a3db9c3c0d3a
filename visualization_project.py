#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据可视化期末考核项目
作者：AI助手
日期：2024年
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import numpy as np
import pandas as pd
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置全局样式
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8-whitegrid')

# 定义主题色彩
THEME_COLORS = {
    'primary': '#2E86AB',      # 主色调 - 深蓝色
    'secondary': '#A23B72',    # 次要色 - 紫红色  
    'accent': '#F18F01',       # 强调色 - 橙色
    'light': '#C73E1D',        # 浅色 - 红色
    'neutral': '#6C757D',      # 中性色 - 灰色
    'background': '#F8F9FA'    # 背景色 - 浅灰色
}

COLOR_PALETTE = [THEME_COLORS['primary'], THEME_COLORS['secondary'], 
                THEME_COLORS['accent'], THEME_COLORS['light'], THEME_COLORS['neutral']]

def read_excel_manually(file_path):
    """手动读取Excel文件的简单实现"""
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    tree = ET.parse(f)
                    root = tree.getroot()
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t is not None:
                            shared_strings.append(t.text)
            except:
                pass
            
            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                
                rows = []
                for row in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'):
                    row_data = []
                    for cell in row.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'):
                        value = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                        if value is not None:
                            cell_type = cell.get('t')
                            if cell_type == 's':  # 共享字符串
                                try:
                                    idx = int(value.text)
                                    row_data.append(shared_strings[idx])
                                except:
                                    row_data.append(value.text)
                            else:
                                row_data.append(value.text)
                        else:
                            row_data.append('')
                    if row_data:
                        rows.append(row_data)
                
                return rows
    except Exception as e:
        print(f'读取失败: {e}')
        return None

def load_datasets():
    """加载所有数据集"""
    data_path = '数据可视化数据集-A'
    datasets = {}
    
    # 加载二手房数据
    file_path = os.path.join(data_path, '二手房数据.xlsx')
    data = read_excel_manually(file_path)
    if data:
        headers = data[0]
        rows = data[1:]
        df_house = pd.DataFrame(rows, columns=headers)
        # 数据类型转换
        df_house['面积（平方米）'] = pd.to_numeric(df_house['面积（平方米）'], errors='coerce')
        df_house['总价（万元）'] = pd.to_numeric(df_house['总价（万元）'], errors='coerce')
        df_house['房龄（年）'] = pd.to_numeric(df_house['房龄（年）'], errors='coerce')
        df_house['单价（元'] = pd.to_numeric(df_house['单价（元'], errors='coerce')
        datasets['house'] = df_house
    
    # 加载餐厅数据
    file_path = os.path.join(data_path, '某餐厅顾客消费记录.xlsx')
    data = read_excel_manually(file_path)
    if data:
        headers = data[0]
        rows = data[1:]
        df_restaurant = pd.DataFrame(rows, columns=headers)
        # 数据类型转换
        df_restaurant['消费金额（元）'] = pd.to_numeric(df_restaurant['消费金额（元）'], errors='coerce')
        df_restaurant['顾客满意度'] = pd.to_numeric(df_restaurant['顾客满意度'], errors='coerce')
        datasets['restaurant'] = df_restaurant
    
    # 加载地铁数据
    file_path = os.path.join(data_path, '2022年北京市工作日早高峰出站量前20的地铁站.xlsx')
    data = read_excel_manually(file_path)
    if data:
        headers = data[0]
        rows = data[1:]
        df_subway = pd.DataFrame(rows, columns=headers)
        # 数据类型转换
        df_subway['经度'] = pd.to_numeric(df_subway['经度'], errors='coerce')
        df_subway['纬度'] = pd.to_numeric(df_subway['纬度'], errors='coerce')
        df_subway['出站量（万人次）'] = pd.to_numeric(df_subway['出站量（万人次）'], errors='coerce')
        datasets['subway'] = df_subway
    
    return datasets

def setup_plot_style(figsize=(12, 8)):
    """设置图表样式"""
    fig, ax = plt.subplots(figsize=figsize)
    ax.set_facecolor(THEME_COLORS['background'])
    fig.patch.set_facecolor('white')
    return fig, ax

# ==================== 二手房数据可视化 ====================

def plot_house_scatter(df_house):
    """图表1：二手房面积vs总价散点图（按区域着色）"""
    fig, ax = setup_plot_style(figsize=(14, 10))

    # 获取区域列表和颜色
    districts = df_house['所在区'].unique()
    colors = plt.cm.Set3(np.linspace(0, 1, len(districts)))

    for i, district in enumerate(districts):
        district_data = df_house[df_house['所在区'] == district]
        ax.scatter(district_data['面积（平方米）'], district_data['总价（万元）'],
                  c=[colors[i]], label=district, alpha=0.7, s=60, edgecolors='white', linewidth=0.5)

    ax.set_xlabel('面积（平方米）', fontsize=14, fontweight='bold')
    ax.set_ylabel('总价（万元）', fontsize=14, fontweight='bold')
    ax.set_title('北京二手房面积与总价关系分析\n（按区域分类）', fontsize=16, fontweight='bold', pad=20)

    # 设置图例
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

    # 添加趋势线
    x = df_house['面积（平方米）'].dropna()
    y = df_house['总价（万元）'].dropna()
    z = np.polyfit(x, y, 1)
    p = np.poly1d(z)
    ax.plot(x, p(x), color=THEME_COLORS['accent'], linestyle='--', linewidth=2, alpha=0.8)

    plt.tight_layout()
    plt.savefig('图表1_二手房面积总价散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_house_boxplot(df_house):
    """图表2：不同区域房价分布箱形图"""
    fig, ax = setup_plot_style(figsize=(14, 8))

    # 计算每个区域的房价数据
    districts = df_house['所在区'].value_counts().head(8).index  # 选择房源最多的8个区域
    plot_data = [df_house[df_house['所在区'] == district]['总价（万元）'].dropna() for district in districts]

    box_plot = ax.boxplot(plot_data, labels=districts, patch_artist=True,
                         boxprops=dict(facecolor=THEME_COLORS['primary'], alpha=0.7),
                         medianprops=dict(color=THEME_COLORS['accent'], linewidth=2),
                         whiskerprops=dict(color=THEME_COLORS['neutral']),
                         capprops=dict(color=THEME_COLORS['neutral']))

    ax.set_xlabel('区域', fontsize=14, fontweight='bold')
    ax.set_ylabel('总价（万元）', fontsize=14, fontweight='bold')
    ax.set_title('北京各区域二手房价格分布对比', fontsize=16, fontweight='bold', pad=20)

    # 旋转x轴标签
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('图表2_各区域房价箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_house_histogram(df_house):
    """图表3：房龄分布直方图"""
    fig, ax = setup_plot_style(figsize=(12, 8))

    # 绘制直方图
    n, bins, patches = ax.hist(df_house['房龄（年）'].dropna(), bins=20,
                              color=THEME_COLORS['primary'], alpha=0.7, edgecolor='white')

    # 为每个柱子设置渐变色
    for i, patch in enumerate(patches):
        patch.set_facecolor(plt.cm.Blues(0.4 + 0.6 * i / len(patches)))

    ax.set_xlabel('房龄（年）', fontsize=14, fontweight='bold')
    ax.set_ylabel('房源数量', fontsize=14, fontweight='bold')
    ax.set_title('北京二手房房龄分布统计', fontsize=16, fontweight='bold', pad=20)

    # 添加统计信息
    mean_age = df_house['房龄（年）'].mean()
    ax.axvline(mean_age, color=THEME_COLORS['accent'], linestyle='--', linewidth=2,
               label=f'平均房龄: {mean_age:.1f}年')
    ax.legend(fontsize=12)

    plt.tight_layout()
    plt.savefig('图表3_房龄分布直方图.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==================== 餐厅数据可视化 ====================

def plot_restaurant_violin(df_restaurant):
    """图表4：不同分店消费金额分布小提琴图"""
    fig, ax = setup_plot_style(figsize=(12, 8))

    # 准备数据
    stores = df_restaurant['分店'].unique()
    plot_data = [df_restaurant[df_restaurant['分店'] == store]['消费金额（元）'].dropna() for store in stores]

    # 绘制小提琴图
    violin_parts = ax.violinplot(plot_data, positions=range(1, len(stores)+1),
                                showmeans=True, showmedians=True)

    # 设置颜色
    for i, pc in enumerate(violin_parts['bodies']):
        pc.set_facecolor(COLOR_PALETTE[i % len(COLOR_PALETTE)])
        pc.set_alpha(0.7)

    ax.set_xticks(range(1, len(stores)+1))
    ax.set_xticklabels(stores)
    ax.set_xlabel('分店', fontsize=14, fontweight='bold')
    ax.set_ylabel('消费金额（元）', fontsize=14, fontweight='bold')
    ax.set_title('各分店顾客消费金额分布对比', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('图表4_分店消费分布小提琴图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_restaurant_bar(df_restaurant):
    """图表5：各分店平均消费金额条形图"""
    fig, ax = setup_plot_style(figsize=(10, 8))

    # 计算各分店平均消费
    avg_consumption = df_restaurant.groupby('分店')['消费金额（元）'].mean().sort_values(ascending=False)

    # 绘制条形图
    bars = ax.bar(avg_consumption.index, avg_consumption.values,
                  color=COLOR_PALETTE[:len(avg_consumption)], alpha=0.8, edgecolor='white')

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{height:.1f}', ha='center', va='bottom', fontweight='bold')

    ax.set_xlabel('分店', fontsize=14, fontweight='bold')
    ax.set_ylabel('平均消费金额（元）', fontsize=14, fontweight='bold')
    ax.set_title('各分店平均消费金额对比', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('图表5_分店平均消费条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_restaurant_scatter(df_restaurant):
    """图表6：餐厅消费金额vs满意度散点图"""
    fig, ax = setup_plot_style(figsize=(12, 8))

    # 按顾客类型分组
    member_data = df_restaurant[df_restaurant['顾客类型'] == '会员']
    regular_data = df_restaurant[df_restaurant['顾客类型'] == '普通顾客']

    ax.scatter(member_data['消费金额（元）'], member_data['顾客满意度'],
              c=THEME_COLORS['primary'], label='会员', alpha=0.7, s=60, edgecolors='white')
    ax.scatter(regular_data['消费金额（元）'], regular_data['顾客满意度'],
              c=THEME_COLORS['secondary'], label='普通顾客', alpha=0.7, s=60, edgecolors='white')

    ax.set_xlabel('消费金额（元）', fontsize=14, fontweight='bold')
    ax.set_ylabel('顾客满意度', fontsize=14, fontweight='bold')
    ax.set_title('顾客消费金额与满意度关系分析', fontsize=16, fontweight='bold', pad=20)
    ax.legend(fontsize=12)

    plt.tight_layout()
    plt.savefig('图表6_消费满意度散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==================== 地铁数据可视化 ====================

def plot_subway_bar(df_subway):
    """图表7：地铁站出站量排名条形图"""
    fig, ax = setup_plot_style(figsize=(14, 8))

    # 按出站量排序
    df_sorted = df_subway.sort_values('出站量（万人次）', ascending=True)

    # 绘制水平条形图
    bars = ax.barh(df_sorted['地铁站'], df_sorted['出站量（万人次）'],
                   color=THEME_COLORS['primary'], alpha=0.8, edgecolor='white')

    # 添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax.text(width + 0.02, bar.get_y() + bar.get_height()/2,
                f'{width:.2f}', ha='left', va='center', fontweight='bold')

    ax.set_xlabel('出站量（万人次）', fontsize=14, fontweight='bold')
    ax.set_ylabel('地铁站', fontsize=14, fontweight='bold')
    ax.set_title('2022年北京市工作日早高峰地铁站出站量排名', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('图表7_地铁站出站量条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_subway_pie(df_subway):
    """图表8：地铁站出站量占比饼图"""
    fig, ax = setup_plot_style(figsize=(10, 10))

    # 选择前10个站点，其余归为"其他"
    top_10 = df_subway.nlargest(10, '出站量（万人次）')
    others_sum = df_subway.nsmallest(10, '出站量（万人次）')['出站量（万人次）'].sum()

    # 准备数据
    labels = list(top_10['地铁站']) + ['其他站点']
    sizes = list(top_10['出站量（万人次）']) + [others_sum]

    # 设置颜色
    colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))

    # 绘制饼图
    wedges, texts, autotexts = ax.pie(sizes, labels=labels, autopct='%1.1f%%',
                                     colors=colors, startangle=90,
                                     textprops={'fontsize': 10})

    ax.set_title('北京地铁站早高峰出站量占比分布', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('图表8_地铁站出站量饼图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_subway_bubble(df_subway):
    """图表9：地铁站地理位置气泡图"""
    fig, ax = setup_plot_style(figsize=(12, 10))

    # 绘制气泡图
    scatter = ax.scatter(df_subway['经度'], df_subway['纬度'],
                        s=df_subway['出站量（万人次）']*100,  # 气泡大小
                        c=df_subway['出站量（万人次）'],
                        cmap='YlOrRd', alpha=0.7, edgecolors='white', linewidth=1)

    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax)
    cbar.set_label('出站量（万人次）', fontsize=12, fontweight='bold')

    # 添加站点标签（只标注前5个）
    top_5 = df_subway.nlargest(5, '出站量（万人次）')
    for _, row in top_5.iterrows():
        ax.annotate(row['地铁站'], (row['经度'], row['纬度']),
                   xytext=(5, 5), textcoords='offset points',
                   fontsize=9, fontweight='bold')

    ax.set_xlabel('经度', fontsize=14, fontweight='bold')
    ax.set_ylabel('纬度', fontsize=14, fontweight='bold')
    ax.set_title('北京地铁站地理分布与出站量关系\n（气泡大小表示出站量）',
                fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('图表9_地铁站地理气泡图.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_all_visualizations():
    """生成所有可视化图表"""
    print("开始生成数据可视化图表...")

    # 加载数据
    datasets = load_datasets()
    print("数据加载完成！")
    for name, df in datasets.items():
        print(f"{name}: {df.shape}")

    print("\n开始生成图表...")

    # 二手房数据可视化
    if 'house' in datasets:
        print("正在生成二手房数据可视化...")
        plot_house_scatter(datasets['house'])
        plot_house_boxplot(datasets['house'])
        plot_house_histogram(datasets['house'])

    # 餐厅数据可视化
    if 'restaurant' in datasets:
        print("正在生成餐厅数据可视化...")
        plot_restaurant_violin(datasets['restaurant'])
        plot_restaurant_bar(datasets['restaurant'])
        plot_restaurant_scatter(datasets['restaurant'])

    # 地铁数据可视化
    if 'subway' in datasets:
        print("正在生成地铁数据可视化...")
        plot_subway_bar(datasets['subway'])
        plot_subway_pie(datasets['subway'])
        plot_subway_bubble(datasets['subway'])

    print("\n所有图表生成完成！")
    print("生成的图表文件：")
    print("1. 图表1_二手房面积总价散点图.png")
    print("2. 图表2_各区域房价箱形图.png")
    print("3. 图表3_房龄分布直方图.png")
    print("4. 图表4_分店消费分布小提琴图.png")
    print("5. 图表5_分店平均消费条形图.png")
    print("6. 图表6_消费满意度散点图.png")
    print("7. 图表7_地铁站出站量条形图.png")
    print("8. 图表8_地铁站出站量饼图.png")
    print("9. 图表9_地铁站地理气泡图.png")

if __name__ == "__main__":
    generate_all_visualizations()
