#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体显示测试脚本
专门用于验证中文字体是否正确显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import platform

def setup_chinese_font():
    """设置中文字体"""
    print("正在配置中文字体...")
    
    # 获取系统信息
    system = platform.system()
    print(f"检测到系统: {system}")
    
    # 获取所有可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    print(f"系统中共有 {len(available_fonts)} 个字体")
    
    # 定义中文字体优先级列表
    if system == "Windows":
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        chinese_fonts = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'SimHei']
    
    # 查找可用的中文字体
    found_fonts = []
    for font in chinese_fonts:
        if font in available_fonts:
            found_fonts.append(font)
            print(f"✓ 找到字体: {font}")
    
    if found_fonts:
        selected_font = found_fonts[0]
        print(f"选择字体: {selected_font}")
        
        # 设置matplotlib字体参数
        plt.rcParams['font.sans-serif'] = [selected_font] + ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.size'] = 12
        
        print("✓ 中文字体配置完成")
        return selected_font
    else:
        print("⚠ 警告: 未找到合适的中文字体")
        return None

def create_test_chart():
    """创建中文测试图表"""
    print("正在创建中文测试图表...")
    
    # 创建测试数据
    categories = ['北京', '上海', '广州', '深圳', '杭州']
    values = [85, 92, 78, 88, 82]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 子图1：柱状图
    bars = ax1.bar(categories, values, color=['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D'])
    ax1.set_title('中国主要城市数据对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('城市', fontsize=14, fontweight='bold')
    ax1.set_ylabel('数值（分）', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}分', ha='center', va='bottom', fontweight='bold')
    
    # 子图2：饼图
    labels = ['一线城市', '新一线城市', '二线城市', '三线城市']
    sizes = [30, 25, 35, 10]
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
    
    wedges, texts, autotexts = ax2.pie(sizes, labels=labels, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax2.set_title('城市等级分布', fontsize=16, fontweight='bold')
    
    # 设置饼图文字
    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig('中文字体显示测试.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 测试图表已保存为 '中文字体显示测试.png'")

def create_comprehensive_test():
    """创建全面的中文显示测试"""
    print("正在创建全面的中文显示测试...")
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 测试各种中文文本
    test_items = [
        ("标题测试", "数据可视化中文字体显示测试", 18, 'bold'),
        ("轴标签测试", "X轴：时间（年） | Y轴：销售额（万元）", 14, 'bold'),
        ("数字测试", "123,456.78万元 | 98.5% | -12.3°C", 12, 'normal'),
        ("特殊符号", "①②③④⑤ | ★☆♠♥♦♣ | ←→↑↓", 12, 'normal'),
        ("标点符号", "你好，世界！这是'测试'内容。", 12, 'normal'),
        ("英中混合", "Hello 世界 | Python 数据分析 | AI 人工智能", 12, 'normal'),
        ("长文本", "这是一段较长的中文文本，用于测试字体在长文本情况下的显示效果。", 10, 'normal')
    ]
    
    y_positions = np.linspace(0.9, 0.1, len(test_items))
    
    for i, (category, text, size, weight) in enumerate(test_items):
        # 类别标签
        ax.text(0.05, y_positions[i], f"{category}:", 
               fontsize=size, fontweight='bold', color='#2E86AB',
               transform=ax.transAxes)
        
        # 测试文本
        ax.text(0.25, y_positions[i], text, 
               fontsize=size, fontweight=weight, color='black',
               transform=ax.transAxes)
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    ax.set_title('中文字体全面显示测试', fontsize=20, fontweight='bold', pad=20)
    
    # 添加边框
    for spine in ax.spines.values():
        spine.set_visible(True)
        spine.set_linewidth(2)
        spine.set_color('#2E86AB')
    
    plt.tight_layout()
    plt.savefig('中文字体全面测试.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 全面测试图表已保存为 '中文字体全面测试.png'")

if __name__ == "__main__":
    # 设置中文字体
    selected_font = setup_chinese_font()
    
    if selected_font:
        print(f"\n使用字体: {selected_font}")
        
        # 创建测试图表
        create_test_chart()
        create_comprehensive_test()
        
        print("\n✅ 中文字体测试完成！")
        print("如果图表中的中文正确显示，说明字体配置成功。")
        print("如果显示为方框或乱码，请检查系统字体安装情况。")
    else:
        print("\n❌ 中文字体配置失败！")
        print("请确保系统中安装了中文字体。")
